#!/bin/bash
# Example curl commands for data.propbolt.com API

# Set your API key
API_KEY="pb_test_4D2SwyR9qikyE6noDywx6BB6rIsAyv8W"

# 1. Health Check (no API key required)
echo "Testing Health Check endpoint..."
curl -s "https://data.propbolt.com/health" | jq

# 2. Property Search
echo -e "\nTesting Property Search endpoint..."
curl -s -X POST "https://data.propbolt.com/v2/PropertySearch" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St",
    "city": "Los Angeles",
    "state": "CA",
    "size": 5
  }' | jq

# 3. Property Detail
echo -e "\nTesting Property Detail endpoint..."
curl -s -X POST "https://data.propbolt.com/v2/PropertyDetail" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St, Los Angeles, CA 90001"
  }' | jq

# 4. Property Comps (v3)
echo -e "\nTesting Property Comps v3 endpoint..."
curl -s -X POST "https://data.propbolt.com/v3/PropertyComps" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St, Los Angeles, CA 90001",
    "max_days_back": 180,
    "max_radius_miles": 1.0,
    "max_results": 10
  }' | jq

# 5. Address Verification
echo -e "\nTesting Address Verification endpoint..."
curl -s -X POST "https://data.propbolt.com/v2/AddressVerification" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "address": "123 Main St, Los Angeles, CA 90001"
  }' | jq

# 6. PropGPT
echo -e "\nTesting PropGPT endpoint..."
curl -s -X POST "https://data.propbolt.com/v2/PropGPT" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Find all properties listed for sale in Herndon Virginia between 600K and 700K"
  }' | jq

# 7. CSV Builder
echo -e "\nTesting CSV Builder endpoint..."
curl -s -X POST "https://data.propbolt.com/v2/CSVBuilder" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "file_name": "property_export_2024",
    "map": ["id", "address", "estimatedValue", "bedrooms", "bathrooms"]
  }' | jq

# 8. Admin - Generate API Key (requires admin token)
echo -e "\nTesting Admin Generate API Key endpoint..."
curl -s -X POST "https://data.propbolt.com/admin/keys/generate" \
  -H "X-Admin-Token: admin_token_placeholder" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "admin-001",
    "name": "Test API Key",
    "tier": "basic",
    "key_type": "test"
  }' | jq

# 9. Admin - List API Keys (requires admin token)
echo -e "\nTesting Admin List API Keys endpoint..."
curl -s -X GET "https://data.propbolt.com/admin/keys" \
  -H "X-Admin-Token: admin_token_placeholder" | jq

# 10. Admin - Get Usage Analytics (requires admin token)
echo -e "\nTesting Admin Usage Analytics endpoint..."
curl -s -X GET "https://data.propbolt.com/admin/usage/KEY_ID" \
  -H "X-Admin-Token: admin_token_placeholder" | jq