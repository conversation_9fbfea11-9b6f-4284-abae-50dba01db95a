#!/usr/bin/env python3
"""
PropBolt API1 Service - Google Cloud Endpoints Implementation
Real Estate Data API Service for api1.propbolt.com using Google Cloud Endpoints
"""

import os
import sys
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from flask import Flask, request, jsonify
from flask_cors import CORS

# Add local modules to path
sys.path.append('./real_estate_api_py')

# Import the original Real Estate API SDK
from real_estate_api_py import Client
from real_estate_api_py.environment import Environment

# Import PropBolt database components (optional for now)
DATABASE_AVAILABLE = False
try:
    from database.services import APIKeyService, UsageTrackingService
    from database.models import APIKey
    DATABASE_AVAILABLE = True
    logger.info("✅ Database components loaded")
except ImportError as e:
    logger.warning(f"⚠️ Database components not available: {e}")
except Exception as e:
    logger.warning(f"⚠️ Database initialization failed: {e}")

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS for PropBolt domains
cors_origins = [
    'https://propbolt.com',
    'https://go.propbolt.com', 
    'https://api.propbolt.com',
    'https://data.propbolt.com',
    'https://api1.propbolt.com',
    'http://localhost:3000'
]
CORS(app, origins=cors_origins, supports_credentials=True)

# Configuration
REAL_ESTATE_API_KEY = os.getenv('REAL_ESTATE_API_KEY', 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914')
REAL_ESTATE_API_URL = os.getenv('REAL_ESTATE_API_URL', 'https://api.realestateapi.com')

# Test database connection on startup (following Go API pattern)
if DATABASE_AVAILABLE:
    try:
        # Simple connection test using DATABASE_URL
        import psycopg2
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            conn = psycopg2.connect(database_url)
            conn.close()
            logger.info("✅ Database connection test successful")
        else:
            logger.warning("⚠️ DATABASE_URL not set - database features disabled")
            DATABASE_AVAILABLE = False
    except Exception as e:
        logger.warning(f"⚠️ Database connection test failed: {e} - using fallback authentication")
        DATABASE_AVAILABLE = False
else:
    logger.warning("⚠️ Database components not available - using fallback authentication")

# Initialize Real Estate API client
try:
    real_estate_client = Client(
        api_key=REAL_ESTATE_API_KEY,
        environment=Environment.PRODUCTION
    )
    logger.info("✅ Real Estate API client initialized")
    CLIENT_AVAILABLE = True
except Exception as e:
    logger.error(f"❌ Real Estate API client initialization failed: {e}")
    real_estate_client = None
    CLIENT_AVAILABLE = False

def validate_api_key(api_key: str) -> tuple[Optional[Dict], Optional[str], Optional[int]]:
    """
    Validate API key using database or fallback to environment variable
    Returns: (key_info, error_message, status_code)
    """
    if not api_key:
        return None, "API key required", 401
    
    # Check fallback API key first for backward compatibility
    fallback_key = os.getenv('API_KEY', REAL_ESTATE_API_KEY)
    if api_key == fallback_key:
        return {
            'id': 'fallback',
            'name': 'Fallback Key',
            'tier': 'unlimited',
            'is_fallback': True
        }, None, None
    
    # Check database-managed API keys if available
    if DATABASE_AVAILABLE:
        try:
            # This would be async in a real implementation, simplified for Flask
            key_info = APIKeyService.validate_api_key(api_key)
            if key_info:
                # Check quotas
                quota_check = UsageTrackingService.check_quota_exceeded(key_info)
                if quota_check.get('daily_quota_exceeded'):
                    return None, "Daily API quota exceeded", 429
                
                return {
                    'id': key_info.id,
                    'name': key_info.name,
                    'tier': key_info.tier,
                    'is_fallback': False,
                    'quota_info': quota_check
                }, None, None
        except Exception as e:
            logger.error(f"Database API key validation failed: {e}")
    
    return None, "Invalid API key", 401

def extract_api_key(request) -> Optional[str]:
    """Extract API key from request headers"""
    # Check x-api-key header (primary)
    api_key = request.headers.get('x-api-key')
    if api_key:
        return api_key
    
    # Check Authorization header (Bearer token)
    auth_header = request.headers.get('Authorization', '')
    if auth_header.startswith('Bearer '):
        return auth_header[7:]  # Remove 'Bearer ' prefix
    
    # Check query parameter as fallback
    return request.args.get('api_key')

def require_api_key(f):
    """Decorator to require API key authentication"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = extract_api_key(request)
        key_info, error_msg, status_code = validate_api_key(api_key)
        
        if error_msg:
            return jsonify({
                'error': error_msg,
                'timestamp': datetime.utcnow().isoformat()
            }), status_code
        
        # Add key info to request context
        request.api_key_info = key_info
        
        # Log usage if database is available and not fallback key
        if DATABASE_AVAILABLE and not key_info.get('is_fallback'):
            try:
                UsageTrackingService.log_api_usage(
                    api_key_id=str(key_info['id']),
                    endpoint=request.endpoint or request.path,
                    method=request.method,
                    status_code=200,  # Will be updated in error handlers if needed
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
            except Exception as e:
                logger.error(f"Failed to log API usage: {e}")
        
        return f(*args, **kwargs)
    
    return decorated_function

def proxy_to_real_estate_api(endpoint_path: str, data: Dict = None) -> tuple[Dict, int]:
    """Proxy request to the Real Estate API"""
    if not CLIENT_AVAILABLE:
        return {
            'error': 'Service temporarily unavailable',
            'message': 'Real Estate API client not available',
            'timestamp': datetime.utcnow().isoformat()
        }, 503
    
    try:
        # Map endpoint paths to SDK methods
        endpoint_mapping = {
            '/v2/PropertySearch': lambda d: real_estate_client.property.search(**d),
            '/v2/PropertyDetail': lambda d: real_estate_client.property.details(**d),
            '/v2/PropertyDetailBulk': lambda d: real_estate_client.property.bulk_details(**d),
            '/v1/PropertyParcel': lambda d: real_estate_client.property.parcel(**d),
            '/v2/PropertyComps': lambda d: real_estate_client.property.comparables(**d),
            '/v3/PropertyComps': lambda d: real_estate_client.property.comparables_advanced(**d),
            '/v2/AutoComplete': lambda d: real_estate_client.address.auto_complete(**d),
            '/v2/AddressVerification': lambda d: real_estate_client.address.verify(**d),
            '/v2/PropGPT': lambda d: real_estate_client.gpt.chat(**d),
            '/v2/CSVBuilder': lambda d: real_estate_client.reporting.generate_csv(**d),
            '/v2/PropertyAvm': lambda d: real_estate_client.avm.get_valuation(**d),
            '/v2/Reports/PropertyLiens': lambda d: real_estate_client.reporting.lien(**d),
            '/v2/PropertyMapping': lambda d: real_estate_client.property.pins(**d),
        }
        
        if endpoint_path not in endpoint_mapping:
            return {
                'error': 'Endpoint not found',
                'message': f'Endpoint {endpoint_path} is not supported',
                'timestamp': datetime.utcnow().isoformat()
            }, 404
        
        # Call the appropriate SDK method
        sdk_method = endpoint_mapping[endpoint_path]
        response = sdk_method(**data) if data else sdk_method()

        # Convert response to dict if it's a Pydantic model
        if hasattr(response, 'model_dump'):
            response_data = response.model_dump()
        elif hasattr(response, 'dict'):
            response_data = response.dict()
        else:
            response_data = response

        # Return the response data
        return {
            'success': True,
            'data': response_data,
            'timestamp': datetime.utcnow().isoformat()
        }, 200
        
    except Exception as e:
        logger.error(f"Real Estate API request failed for {endpoint_path}: {e}")
        return {
            'error': 'API request failed',
            'message': str(e),
            'endpoint': endpoint_path,
            'timestamp': datetime.utcnow().isoformat()
        }, 500

# Root endpoint
@app.route('/', methods=['GET'])
def root():
    """API information and available endpoints"""
    return jsonify({
        'message': 'PropBolt API1 Service - Google Cloud Endpoints',
        'version': '1.0.0',
        'description': 'Real Estate Data API Service for api1.propbolt.com',
        'endpoints': {
            'health': 'GET /health',
            'property_search': 'POST /v2/PropertySearch',
            'property_details': 'POST /v2/PropertyDetail',
            'property_detail_bulk': 'POST /v2/PropertyDetailBulk',
            'property_parcel': 'POST /v1/PropertyParcel',
            'property_comps_v2': 'POST /v2/PropertyComps',
            'property_comps_v3': 'POST /v3/PropertyComps',
            'autocomplete': 'POST /v2/AutoComplete',
            'address_verification': 'POST /v2/AddressVerification',
            'propgpt': 'POST /v2/PropGPT',
            'csv_builder': 'POST /v2/CSVBuilder',
            'property_avm': 'POST /v2/PropertyAvm',
            'property_liens': 'POST /v2/Reports/PropertyLiens',
            'property_mapping': 'POST /v2/PropertyMapping'
        },
        'documentation': '/docs',
        'timestamp': datetime.utcnow().isoformat()
    })

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for Google Cloud"""
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'service': 'api1.propbolt.com',
        'api_key_configured': bool(REAL_ESTATE_API_KEY),
        'sdk_client_available': CLIENT_AVAILABLE,
        'database_available': DATABASE_AVAILABLE
    }
    
    # Add database health if available
    if DATABASE_AVAILABLE:
        try:
            # Note: check_database_health is async, so we'll skip it for now
            health_status['database'] = {'status': 'available', 'note': 'Database components loaded'}
        except Exception as e:
            health_status['database'] = {'status': 'unhealthy', 'error': str(e)}
    
    return jsonify(health_status)

# API Documentation endpoint
@app.route('/docs', methods=['GET'])
def api_docs():
    """Swagger UI API documentation"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>PropBolt API1 Service - API Documentation</title>
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui.css" />
        <style>
            html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
            *, *:before, *:after { box-sizing: inherit; }
            body { margin:0; background: #fafafa; }
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-bundle.js"></script>
        <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-standalone-preset.js"></script>
        <script>
            window.onload = function() {
                const ui = SwaggerUIBundle({
                    url: '/openapi.json',
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIStandalonePreset
                    ],
                    plugins: [
                        SwaggerUIBundle.plugins.DownloadUrl
                    ],
                    layout: "StandaloneLayout"
                });
            };
        </script>
    </body>
    </html>
    """

# OpenAPI specification endpoint
@app.route('/openapi.json', methods=['GET'])
def openapi_spec():
    """OpenAPI specification for the API"""
    return jsonify({
        "openapi": "3.0.0",
        "info": {
            "title": "PropBolt API1 Service",
            "description": "Real Estate Data API Service for api1.propbolt.com",
            "version": "1.0.0",
            "contact": {
                "name": "PropBolt Support",
                "email": "<EMAIL>"
            }
        },
        "servers": [
            {
                "url": f"https://{request.host}",
                "description": "Production server"
            }
        ],
        "security": [
            {
                "ApiKeyAuth": []
            }
        ],
        "components": {
            "securitySchemes": {
                "ApiKeyAuth": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "x-api-key",
                    "description": "API key for authentication"
                }
            }
        },
        "paths": {
            "/health": {
                "get": {
                    "summary": "Health Check",
                    "description": "Health check endpoint for monitoring",
                    "security": [],
                    "responses": {
                        "200": {
                            "description": "Service health status"
                        }
                    }
                }
            },
            "/v2/PropertySearch": {
                "post": {
                    "summary": "Property Search API",
                    "description": "Advanced property search and filtering",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string"},
                                        "city": {"type": "string"},
                                        "state": {"type": "string"},
                                        "zip": {"type": "string"},
                                        "size": {"type": "integer", "default": 50}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property search results"},
                        "401": {"description": "Unauthorized - Invalid API key"},
                        "429": {"description": "Rate limit exceeded"}
                    }
                }
            },
            "/v2/PropertyDetail": {
                "post": {
                    "summary": "Property Detail API",
                    "description": "Get comprehensive property information",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "address": {"type": "string"},
                                        "id": {"type": "string"},
                                        "apn": {"type": "string"},
                                        "exact_match": {"type": "boolean", "default": False}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {"description": "Property details"},
                        "401": {"description": "Unauthorized - Invalid API key"},
                        "429": {"description": "Rate limit exceeded"}
                    }
                }
            }
        }
    })

# API Endpoints - Real Estate Data Services

@app.route('/v2/PropertySearch', methods=['POST'])
@require_api_key
def property_search():
    """Property Search API - Searchable API for list building and filtering"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertySearch', data)
    return jsonify(result), status_code

@app.route('/v2/PropertyDetail', methods=['POST'])
@require_api_key
def property_detail():
    """Property Detail API - Get comprehensive property information"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyDetail', data)
    return jsonify(result), status_code

@app.route('/v2/PropertyDetailBulk', methods=['POST'])
@require_api_key
def property_detail_bulk():
    """Property Detail Bulk API - Retrieve up to 1000 properties at once"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyDetailBulk', data)
    return jsonify(result), status_code

@app.route('/v1/PropertyParcel', methods=['POST'])
@require_api_key
def property_parcel():
    """Property Boundary API - Returns parcel boundaries in GEOJSON format"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v1/PropertyParcel', data)
    return jsonify(result), status_code

@app.route('/v2/PropertyComps', methods=['POST'])
@require_api_key
def property_comps_v2():
    """Property Comparables API v2 - Generate property comparables for valuation"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyComps', data)
    return jsonify(result), status_code

@app.route('/v3/PropertyComps', methods=['POST'])
@require_api_key
def property_comps_v3():
    """Property Comparables API v3 - Advanced comparables with customizable parameters"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v3/PropertyComps', data)
    return jsonify(result), status_code

@app.route('/v2/AutoComplete', methods=['POST'])
@require_api_key
def autocomplete():
    """AutoComplete API - Property search based on incomplete address parts"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/AutoComplete', data)
    return jsonify(result), status_code

@app.route('/v2/AddressVerification', methods=['POST'])
@require_api_key
def address_verification():
    """Address Verification API - Verify 1-100 addresses for accuracy"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/AddressVerification', data)
    return jsonify(result), status_code

@app.route('/v2/PropGPT', methods=['POST'])
@require_api_key
def propgpt():
    """PropGPT API - Natural language property search using AI"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropGPT', data)
    return jsonify(result), status_code

@app.route('/v2/CSVBuilder', methods=['POST'])
@require_api_key
def csv_builder():
    """CSV Generator API - Generate CSV exports of property data"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/CSVBuilder', data)
    return jsonify(result), status_code

@app.route('/v2/PropertyAvm', methods=['POST'])
@require_api_key
def property_avm():
    """Lender Grade AVM API - Get precise property valuations"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyAvm', data)
    return jsonify(result), status_code

@app.route('/v2/Reports/PropertyLiens', methods=['POST'])
@require_api_key
def property_liens():
    """Involuntary Liens API - Get property lien information"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/Reports/PropertyLiens', data)
    return jsonify(result), status_code

@app.route('/v2/PropertyMapping', methods=['POST'])
@require_api_key
def property_mapping():
    """Mapping (Pins) API - Create map pins for PropTech applications"""
    data = request.get_json() or {}
    result, status_code = proxy_to_real_estate_api('/v2/PropertyMapping', data)
    return jsonify(result), status_code

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Endpoint not found',
        'message': 'The requested endpoint does not exist',
        'timestamp': datetime.utcnow().isoformat()
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred',
        'timestamp': datetime.utcnow().isoformat()
    }), 500

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('DEBUG_MODE', 'false').lower() == 'true'

    logger.info(f"Starting PropBolt API1 Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
