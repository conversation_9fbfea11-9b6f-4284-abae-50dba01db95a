#!/bin/bash

# Get the deployed URL
DEPLOYED_URL=$(gcloud app describe --service=api --format="value(defaultHostname)")
echo "Deployed URL: $DEPLOYED_URL"

# Test health endpoint
echo "Testing health endpoint..."
curl -s "https://$DEPLOYED_URL/health" | jq .

# Test property endpoint with address
echo "Testing property endpoint with address..."
curl -s "https://$DEPLOYED_URL/property?address=123+Main+St+San+Francisco+CA" | jq .

# Test rent zestimate endpoint
echo "Testing rent zestimate endpoint..."
curl -s "https://$DEPLOYED_URL/rentZestimate?address=123+Main+St+San+Francisco+CA" | jq .

# Test auth health endpoint
echo "Testing auth health endpoint..."
curl -s "https://$DEPLOYED_URL/auth/health" | jq .

# Check logs for errors
echo "Checking recent logs..."
gcloud app logs read --service=api --limit=10